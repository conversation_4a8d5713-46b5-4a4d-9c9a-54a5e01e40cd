import { Router } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database'
import { AuthenticatedRequest, requirePermission } from '@/middleware/auth'
import { RBACService } from '@/services/auth'
import { AgentFlowSchema } from '@/services/agent-engine'
import { logger } from '@/utils/logger'

const router = Router()

// Validation schemas
const CreateAgentSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  config: AgentFlowSchema,
  tenantId: z.string().optional()
})

const UpdateAgentSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  config: AgentFlowSchema.optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'TRAINING', 'ERROR']).optional()
})

const AssignToolSchema = z.object({
  toolId: z.string(),
  config: z.record(z.any()).optional(),
  priority: z.number().default(0)
})

// Get all agents
router.get('/', requirePermission('agents:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user

    const agents = await prisma.agent.findMany({
      where: user.tenantId ? { tenantId: user.tenantId } : {},
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        tools: {
          include: {
            tool: {
              select: { id: true, name: true, type: true }
            }
          }
        },
        _count: {
          select: { sessions: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const filteredAgents = RBACService.filterByTenant(agents, user.tenantId ?? null)

    res.json({
      success: true,
      data: filteredAgents
    })
  } catch (error) {
    next(error)
  }
})

// Get agent by ID
router.get('/:id', requirePermission('agents:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Agent ID is required' })
    }

    const agent = await prisma.agent.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        tools: {
          include: {
            tool: true
          }
        },
        sessions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            status: true,
            createdAt: true,
            updatedAt: true
          }
        }
      }
    })

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, agent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    res.json({
      success: true,
      data: agent
    })
  } catch (error) {
    next(error)
  }
})

// Create agent
router.post('/', requirePermission('agents:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { name, description, config, tenantId } = CreateAgentSchema.parse(req.body)

    const agent = await prisma.agent.create({
      data: {
        name,
        description: description ?? null,
        config,
        userId: user.userId,
        tenantId: tenantId || user.tenantId || null
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    logger.info(`Agent created: ${agent.name} by user ${user.userId}`)

    res.status(201).json({
      success: true,
      data: agent
    })
  } catch (error) {
    next(error)
  }
})

// Update agent
router.put('/:id', requirePermission('agents:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const updates = UpdateAgentSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({ error: 'Agent ID is required' })
    }

    // Check if agent exists and user has access
    const existingAgent = await prisma.agent.findUnique({
      where: { id }
    })

    if (!existingAgent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, existingAgent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Clean up undefined values for Prisma
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    )

    const agent = await prisma.agent.update({
      where: { id },
      data: {
        ...cleanUpdates,
        updatedAt: new Date()
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        tools: {
          include: {
            tool: true
          }
        }
      }
    })

    logger.info(`Agent updated: ${agent.name} by user ${user.userId}`)

    res.json({
      success: true,
      data: agent
    })
  } catch (error) {
    next(error)
  }
})

// Delete agent
router.delete('/:id', requirePermission('agents:delete'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Agent ID is required' })
    }

    // Check if agent exists and user has access
    const existingAgent = await prisma.agent.findUnique({
      where: { id }
    })

    if (!existingAgent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, existingAgent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    await prisma.agent.delete({
      where: { id }
    })

    logger.info(`Agent deleted: ${existingAgent.name} by user ${user.userId}`)

    res.json({
      success: true,
      message: 'Agent deleted successfully'
    })
  } catch (error) {
    next(error)
  }
})

// Assign tool to agent
router.post('/:id/tools', requirePermission('agents:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const { toolId, config, priority } = AssignToolSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({ error: 'Agent ID is required' })
    }

    // Check if agent exists and user has access
    const agent = await prisma.agent.findUnique({
      where: { id }
    })

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, agent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Check if tool exists
    const tool = await prisma.tool.findUnique({
      where: { id: toolId }
    })

    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    // Create or update agent-tool relationship
    const agentTool = await prisma.agentTool.upsert({
      where: {
        agentId_toolId: {
          agentId: id,
          toolId
        }
      },
      update: {
        config: config || {},
        priority
      },
      create: {
        agentId: id,
        toolId,
        config: config || {},
        priority
      },
      include: {
        tool: true
      }
    })

    res.json({
      success: true,
      data: agentTool
    })
  } catch (error) {
    next(error)
  }
})

// Remove tool from agent
router.delete('/:id/tools/:toolId', requirePermission('agents:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id, toolId } = req.params

    if (!id || !toolId) {
      return res.status(400).json({ error: 'Agent ID and Tool ID are required' })
    }

    // Check if agent exists and user has access
    const agent = await prisma.agent.findUnique({
      where: { id }
    })

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, agent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    await prisma.agentTool.delete({
      where: {
        agentId_toolId: {
          agentId: id,
          toolId
        }
      }
    })

    res.json({
      success: true,
      message: 'Tool removed from agent successfully'
    })
  } catch (error) {
    next(error)
  }
})

// Get agent execution status
router.get('/:id/status', requirePermission('agents:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Agent ID is required' })
    }

    const agent = await prisma.agent.findUnique({
      where: { id },
      include: {
        sessions: {
          where: { status: 'ACTIVE' },
          select: {
            id: true,
            userId: true,
            status: true,
            createdAt: true
          }
        }
      }
    })

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, agent.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    res.json({
      success: true,
      data: {
        agentId: agent.id,
        status: agent.status,
        activeSessions: agent.sessions.length,
        sessions: agent.sessions
      }
    })
  } catch (error) {
    next(error)
  }
})

export default router
