import { Request, Response, NextFunction } from 'express'
import { AuthService, JWTPayload, RBACService } from '@/services/auth'
import { UserRole } from '@prisma/client'

export interface AuthenticatedRequest extends Request {
  user: JWTPayload
}

export function authMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No token provided' })
      return
    }

    const token = authHeader.substring(7)
    const payload = AuthService.verifyToken(token)

      ; (req as AuthenticatedRequest).user = payload
    next()
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' })
  }
}

export function requireRole(roles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as AuthenticatedRequest).user

    if (!user || !roles.includes(user.role)) {
      res.status(403).json({ error: 'Insufficient permissions' })
      return
    }

    next()
  }
}

export function requirePermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as AuthenticatedRequest).user

    if (!user || !RBACService.hasPermission(user.role, permission)) {
      res.status(403).json({ error: 'Insufficient permissions' })
      return
    }

    next()
  }
}

export function requireTenantAccess(req: Request, res: Response, next: NextFunction): void {
  const user = (req as AuthenticatedRequest).user
  const resourceTenantId = req.params.tenantId || req.body.tenantId || null

  if (!RBACService.canAccessTenant(user.tenantId ?? null, resourceTenantId)) {
    res.status(403).json({ error: 'Tenant access denied' })
    return
  }

  next()
}
