import React, { useState, useEffect } from 'react'
import { use<PERSON>arams, useNavigate } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  ArrowLeftIcon,
  ScaleIcon,
  PlayIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { api } from '@/services/api'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { AgentForm, FlowNode } from '@/types'
import { toast } from 'react-hot-toast'

const agentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  config: z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    nodes: z.array(z.any()),
    variables: z.record(z.any()).optional(),
    settings: z.object({
      maxExecutionTime: z.number().optional(),
      maxIterations: z.number().optional(),
      enableLogging: z.boolean().optional(),
      retryAttempts: z.number().optional()
    }).optional()
  })
})

export default function AgentBuilderPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const isEditing = !!id

  const [nodes, setNodes] = useState<FlowNode[]>([])
  const [selectedNode, setSelectedNode] = useState<FlowNode | null>(null)

  // Fetch existing agent if editing
  const { data: agent, isLoading } = useQuery({
    queryKey: ['agent', id],
    queryFn: () => api.getAgent(id!),
    enabled: isEditing
  })

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<AgentForm>({
    resolver: zodResolver(agentSchema),
    defaultValues: {
      name: '',
      description: '',
      config: {
        id: crypto.randomUUID(),
        name: '',
        description: '',
        nodes: [],
        variables: {},
        settings: {
          maxExecutionTime: 300000,
          maxIterations: 100,
          enableLogging: true,
          retryAttempts: 3
        }
      }
    }
  })

  // Initialize form with existing agent data
  useEffect(() => {
    if (agent) {
      setValue('name', agent.name)
      setValue('description', agent.description || '')
      setValue('config', agent.config)
      setNodes(agent.config.nodes || [])
    }
  }, [agent, setValue])

  // Update config nodes when nodes change
  useEffect(() => {
    setValue('config.nodes', nodes)
  }, [nodes, setValue])

  const createAgentMutation = useMutation({
    mutationFn: api.createAgent,
    onSuccess: (data) => {
      toast.success('Agent created successfully')
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      navigate(`/agents/${data.id}`)
    }
  })

  const updateAgentMutation = useMutation({
    mutationFn: (data: AgentForm) => api.updateAgent(id!, data),
    onSuccess: () => {
      toast.success('Agent updated successfully')
      queryClient.invalidateQueries({ queryKey: ['agent', id] })
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      navigate(`/agents/${id}`)
    }
  })

  const onSubmit = (data: AgentForm) => {
    // Update config name to match agent name
    data.config.name = data.name
    data.config.description = data.description

    if (isEditing) {
      updateAgentMutation.mutate(data)
    } else {
      createAgentMutation.mutate(data)
    }
  }

  const addNode = (type: FlowNode['type']) => {
    const newNode: FlowNode = {
      id: crypto.randomUUID(),
      type,
      position: { x: 100 + nodes.length * 200, y: 100 },
      data: getDefaultNodeData(type),
      connections: []
    }
    setNodes([...nodes, newNode])
    setSelectedNode(newNode)
  }

  const updateNode = (nodeId: string, updates: Partial<FlowNode>) => {
    setNodes(nodes.map(node => 
      node.id === nodeId ? { ...node, ...updates } : node
    ))
    if (selectedNode?.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates })
    }
  }

  const deleteNode = (nodeId: string) => {
    setNodes(nodes.filter(node => node.id !== nodeId))
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/agents')}
            className="btn btn-ghost"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditing ? 'Edit Agent' : 'Create Agent'}
            </h1>
            <p className="text-gray-600">
              {isEditing ? 'Modify your agent configuration' : 'Build a new AI agent with custom logic flows'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            type="button"
            className="btn btn-outline"
            onClick={() => {
              // Test agent logic
              toast('Agent testing not implemented yet')
            }}
          >
            <PlayIcon className="h-5 w-5 mr-2" />
            Test
          </button>
          
          <button
            onClick={handleSubmit(onSubmit)}
            disabled={createAgentMutation.isPending || updateAgentMutation.isPending}
            className="btn btn-primary"
          >
            {(createAgentMutation.isPending || updateAgentMutation.isPending) ? (
              <LoadingSpinner size="sm" color="white" className="mr-2" />
            ) : (
              <ScaleIcon className="h-5 w-5 mr-2" />
            )}
            {isEditing ? 'Update' : 'Create'} Agent
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Configuration Panel */}
          <div className="lg:col-span-1 space-y-6">
            {/* Basic Info */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Agent Name
                  </label>
                  <input
                    {...register('name')}
                    className="input"
                    placeholder="Enter agent name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    {...register('description')}
                    rows={3}
                    className="textarea"
                    placeholder="Describe what this agent does"
                  />
                </div>
              </div>
            </div>

            {/* Settings */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Execution Time (ms)
                  </label>
                  <input
                    {...register('config.settings.maxExecutionTime', { valueAsNumber: true })}
                    type="number"
                    className="input"
                    placeholder="300000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Iterations
                  </label>
                  <input
                    {...register('config.settings.maxIterations', { valueAsNumber: true })}
                    type="number"
                    className="input"
                    placeholder="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Retry Attempts
                  </label>
                  <input
                    {...register('config.settings.retryAttempts', { valueAsNumber: true })}
                    type="number"
                    className="input"
                    placeholder="3"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    {...register('config.settings.enableLogging')}
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Enable Logging
                  </label>
                </div>
              </div>
            </div>

            {/* Node Palette */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Nodes</h3>
              
              <div className="space-y-2">
                {[
                  { type: 'start', label: 'Start', color: 'bg-green-100 text-green-800' },
                  { type: 'condition', label: 'Condition', color: 'bg-yellow-100 text-yellow-800' },
                  { type: 'action', label: 'Action', color: 'bg-blue-100 text-blue-800' },
                  { type: 'tool', label: 'Tool', color: 'bg-purple-100 text-purple-800' },
                  { type: 'provider', label: 'Provider', color: 'bg-indigo-100 text-indigo-800' },
                  { type: 'end', label: 'End', color: 'bg-red-100 text-red-800' }
                ].map(({ type, label, color }) => (
                  <button
                    key={type}
                    type="button"
                    onClick={() => addNode(type as FlowNode['type'])}
                    className={`w-full p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors text-left ${color}`}
                  >
                    <PlusIcon className="h-4 w-4 inline mr-2" />
                    {label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Flow Canvas */}
          <div className="lg:col-span-2">
            <div className="card h-96">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Flow Canvas</h3>
                <div className="text-sm text-gray-500">
                  {nodes.length} nodes
                </div>
              </div>
              
              <div className="relative h-full bg-gray-50 rounded-lg overflow-hidden">
                {nodes.length === 0 ? (
                  <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <PlusIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>Add nodes to start building your flow</p>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 space-y-4">
                    {nodes.map((node, index) => (
                      <div
                        key={node.id}
                        onClick={() => setSelectedNode(node)}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedNode?.id === node.id
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-gray-200 bg-white hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-sm font-medium capitalize">{node.type}</span>
                            <p className="text-xs text-gray-500">Node {index + 1}</p>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteNode(node.id)
                            }}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Node Properties */}
          <div className="lg:col-span-1">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Node Properties</h3>
              
              {selectedNode ? (
                <NodePropertiesPanel
                  node={selectedNode}
                  onUpdate={(updates) => updateNode(selectedNode.id, updates)}
                />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Select a node to edit its properties</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  )
}

function getDefaultNodeData(type: FlowNode['type']): Record<string, any> {
  switch (type) {
    case 'start':
      return { variables: {} }
    case 'condition':
      return { condition: '', trueTarget: '', falseTarget: '' }
    case 'action':
      return { action: 'set_variable', parameters: {} }
    case 'tool':
      return { toolId: '', input: {}, outputVariable: '' }
    case 'provider':
      return { providerId: '', messages: [], outputVariable: '', config: {} }
    case 'end':
      return {}
    default:
      return {}
  }
}

interface NodePropertiesPanelProps {
  node: FlowNode
  onUpdate: (updates: Partial<FlowNode>) => void
}

function NodePropertiesPanel({ node, onUpdate }: NodePropertiesPanelProps) {
  const updateData = (key: string, value: any) => {
    onUpdate({
      data: { ...node.data, [key]: value }
    })
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Node Type
        </label>
        <input
          value={node.type}
          disabled
          className="input bg-gray-50"
        />
      </div>

      {node.type === 'condition' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Condition
          </label>
          <textarea
            value={node.data.condition || ''}
            onChange={(e) => updateData('condition', e.target.value)}
            className="textarea"
            placeholder="Enter condition logic"
            rows={3}
          />
        </div>
      )}

      {node.type === 'action' && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Action Type
            </label>
            <select
              value={node.data.action || 'set_variable'}
              onChange={(e) => updateData('action', e.target.value)}
              className="select"
            >
              <option value="set_variable">Set Variable</option>
              <option value="add_memory">Add Memory</option>
              <option value="log_message">Log Message</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Parameters (JSON)
            </label>
            <textarea
              value={JSON.stringify(node.data.parameters || {}, null, 2)}
              onChange={(e) => {
                try {
                  const params = JSON.parse(e.target.value)
                  updateData('parameters', params)
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              className="textarea font-mono text-sm"
              rows={4}
            />
          </div>
        </>
      )}

      {node.type === 'tool' && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tool ID
            </label>
            <input
              value={node.data.toolId || ''}
              onChange={(e) => updateData('toolId', e.target.value)}
              className="input"
              placeholder="Enter tool ID"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Output Variable
            </label>
            <input
              value={node.data.outputVariable || ''}
              onChange={(e) => updateData('outputVariable', e.target.value)}
              className="input"
              placeholder="Variable name for output"
            />
          </div>
        </>
      )}

      {node.type === 'provider' && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Provider ID
            </label>
            <input
              value={node.data.providerId || ''}
              onChange={(e) => updateData('providerId', e.target.value)}
              className="input"
              placeholder="Enter provider ID"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <input
              value={node.data.model || ''}
              onChange={(e) => updateData('model', e.target.value)}
              className="input"
              placeholder="e.g., gpt-4"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Output Variable
            </label>
            <input
              value={node.data.outputVariable || ''}
              onChange={(e) => updateData('outputVariable', e.target.value)}
              className="input"
              placeholder="Variable name for response"
            />
          </div>
        </>
      )}
    </div>
  )
}
