import React, { useState } from 'react'
import { ChevronDown, ChevronRight, Co<PERSON>, Check } from 'lucide-react'
import { Button } from './Button'

interface JsonViewerProps {
  data: any
  maxDepth?: number
  className?: string
}

const JsonViewer: React.FC<JsonViewerProps> = ({ data, maxDepth = 10, className = '' }) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2))
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  return (
    <div className={`relative ${className}`}>
      <div className="absolute top-2 right-2 z-10">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-8 w-8 p-0"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-600" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
      <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
        <JsonNode data={data} depth={0} maxDepth={maxDepth} />
      </div>
    </div>
  )
}

interface JsonNodeProps {
  data: any
  depth: number
  maxDepth: number
  name?: string
}

const JsonNode: React.FC<JsonNodeProps> = ({ data, depth, maxDepth, name }) => {
  const [isExpanded, setIsExpanded] = useState(depth < 2)

  if (depth > maxDepth) {
    return <span className="text-gray-500">...</span>
  }

  const renderValue = (value: any, key?: string) => {
    if (value === null) {
      return <span className="text-gray-500">null</span>
    }

    if (typeof value === 'boolean') {
      return <span className="text-blue-600">{value.toString()}</span>
    }

    if (typeof value === 'number') {
      return <span className="text-purple-600">{value}</span>
    }

    if (typeof value === 'string') {
      return <span className="text-green-600">"{value}"</span>
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-500">[]</span>
      }

      return (
        <div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center text-gray-700 hover:text-gray-900"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <span className="text-gray-500">[{value.length}]</span>
          </button>
          {isExpanded && (
            <div className="ml-4 mt-1">
              {value.map((item, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-500">{index}: </span>
                  <JsonNode
                    data={item}
                    depth={depth + 1}
                    maxDepth={maxDepth}
                    name={index.toString()}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }

    if (typeof value === 'object') {
      const keys = Object.keys(value)
      if (keys.length === 0) {
        return <span className="text-gray-500">{'{}'}</span>
      }

      return (
        <div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center text-gray-700 hover:text-gray-900"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 mr-1" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1" />
            )}
            <span className="text-gray-500">{`{${keys.length}}`}</span>
          </button>
          {isExpanded && (
            <div className="ml-4 mt-1">
              {keys.map((key) => (
                <div key={key} className="mb-1">
                  <span className="text-blue-800 font-medium">"{key}"</span>
                  <span className="text-gray-500">: </span>
                  <JsonNode
                    data={value[key]}
                    depth={depth + 1}
                    maxDepth={maxDepth}
                    name={key}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }

    return <span className="text-red-600">Unknown type</span>
  }

  return (
    <div className="font-mono text-sm">
      {name && depth > 0 && (
        <>
          <span className="text-blue-800 font-medium">"{name}"</span>
          <span className="text-gray-500">: </span>
        </>
      )}
      {renderValue(data)}
    </div>
  )
}

export default JsonViewer
