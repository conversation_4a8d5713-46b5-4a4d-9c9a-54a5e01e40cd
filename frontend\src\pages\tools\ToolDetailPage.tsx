import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Edit, Play, Trash2, Activity, Settings, Code } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Tool } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import  Badge  from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import JsonViewer from '@/components/ui/JsonViewer'

const ToolDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [tool, setTool] = useState<Tool | null>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [executing, setExecuting] = useState(false)
  const [executionResult, setExecutionResult] = useState<any>(null)

  useEffect(() => {
    if (id) {
      loadTool()
      loadStats()
    }
  }, [id])

  const loadTool = async () => {
    try {
      setLoading(true)
      const data = await api.getTool(id!)
      setTool(data)
    } catch (error) {
      console.error('Failed to load tool:', error)
      toast.error('Failed to load tool')
      navigate('/tools')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const data = await api.getToolStats(id!)
      setStats(data)
    } catch (error) {
      console.error('Failed to load tool stats:', error)
    }
  }

  const handleDelete = async () => {
    if (!tool) return

    try {
      setDeleting(true)
      await api.deleteTool(tool.id)
      toast.success('Tool deleted successfully')
      navigate('/tools')
    } catch (error) {
      console.error('Failed to delete tool:', error)
      toast.error('Failed to delete tool')
    } finally {
      setDeleting(false)
    }
  }

  const handleExecute = async () => {
    if (!tool) return

    try {
      setExecuting(true)
      // For demo purposes, execute with empty input
      const result = await api.executeTool(tool.id, {})
      setExecutionResult(result)
      toast.success('Tool executed successfully')
    } catch (error) {
      console.error('Failed to execute tool:', error)
      toast.error('Failed to execute tool')
    } finally {
      setExecuting(false)
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'INTERNAL': return 'bg-blue-100 text-blue-800'
      case 'EXTERNAL': return 'bg-green-100 text-green-800'
      case 'API': return 'bg-purple-100 text-purple-800'
      case 'WEBHOOK': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!tool) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">Tool not found</div>
        <Button onClick={() => navigate('/tools')}>
          Back to Tools
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/tools')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge className={getTypeColor(tool.type)}>
                {tool.type}
              </Badge>
              <Badge className={tool.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {tool.status === 'ACTIVE' ? 'Active' : 'Inactive'}
              </Badge>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExecute} disabled={executing}>
            <Play className="h-4 w-4 mr-2" />
            {executing ? 'Executing...' : 'Execute'}
          </Button>
          <Button variant="outline" onClick={() => navigate(`/tools/${tool.id}/edit`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="schema">Schema</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">
                {tool.description || 'No description available'}
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <Badge className={getTypeColor(tool.type)}>{tool.type}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge className={tool.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {tool.status === 'ACTIVE' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(tool.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Updated:</span>
                  <span>{new Date(tool.updatedAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>

            {executionResult && (
              <Card>
                <CardHeader>
                  <CardTitle>Last Execution Result</CardTitle>
                </CardHeader>
                <CardContent>
                  <JsonViewer data={executionResult} />
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="schema" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Tool Schema
              </CardTitle>
            </CardHeader>
            <CardContent>
              <JsonViewer data={tool.schema} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <JsonViewer data={tool.config} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Usage Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats ? (
                <JsonViewer data={stats} />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No statistics available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Tool</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{tool.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ToolDetailPage
