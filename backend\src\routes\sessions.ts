import { Router } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database'
import { AuthenticatedRequest, requirePermission } from '@/middleware/auth'
import { RBACService } from '@/services/auth'
import { SessionManager } from '@/config/redis'
import { AgentExecutionEngine } from '@/services/agent-engine'
import { logger } from '@/utils/logger'

const router = Router()

// Validation schemas
const CreateSessionSchema = z.object({
  agentId: z.string().optional(),
  providerId: z.string().optional(),
  context: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional()
})

const UpdateSessionSchema = z.object({
  context: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'EXPIRED', 'TERMINATED']).optional()
})

const StartAgentSchema = z.object({
  agentId: z.string(),
  variables: z.record(z.any()).optional()
})

// Get all sessions
router.get('/', requirePermission('sessions:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { status, agentId, limit = '50', offset = '0' } = req.query

    const where: any = { userId: user.userId }
    if (status) where.status = status
    if (agentId) where.agentId = agentId

    const sessions = await prisma.session.findMany({
      where,
      include: {
        agent: {
          select: { id: true, name: true, status: true }
        },
        provider: {
          select: { id: true, name: true, type: true }
        },
        _count: {
          select: { events: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string)
    })

    const total = await prisma.session.count({ where })

    res.json({
      success: true,
      data: {
        sessions,
        pagination: {
          total,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: total > parseInt(offset as string) + sessions.length
        }
      }
    })
  } catch (error) {
    next(error)
  }
})

// Get session by ID
router.get('/:id', requirePermission('sessions:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Session ID is required' })
    }

    const session = await prisma.session.findUnique({
      where: { id },
      include: {
        agent: {
          select: { id: true, name: true, status: true, config: true }
        },
        provider: {
          select: { id: true, name: true, type: true }
        },
        events: {
          orderBy: { timestamp: 'asc' },
          take: 100 // Limit events for performance
        }
      }
    })

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (session.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Get session context from Redis
    const redisContext = await SessionManager.getContext(id)
    const redisMemories = await SessionManager.getMemories(id)

    res.json({
      success: true,
      data: {
        ...session,
        redisContext,
        memories: redisMemories
      }
    })
  } catch (error) {
    next(error)
  }
})

// Create session
router.post('/', requirePermission('sessions:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { agentId, providerId, context, metadata } = CreateSessionSchema.parse(req.body)

    // Validate agent access if provided
    if (agentId) {
      const agent = await prisma.agent.findFirst({
        where: {
          id: agentId,
          OR: [
            { userId: user.userId },
            { tenantId: user.tenantId }
          ]
        }
      })

      if (!agent) {
        return res.status(404).json({ error: 'Agent not found or access denied' })
      }
    }

    // Validate provider access if provided
    if (providerId) {
      const provider = await prisma.provider.findFirst({
        where: {
          id: providerId,
          OR: [
            { tenantId: user.tenantId },
            { tenantId: null } // Global providers
          ]
        }
      })

      if (!provider) {
        return res.status(404).json({ error: 'Provider not found or access denied' })
      }
    }

    const session = await prisma.session.create({
      data: {
        userId: user.userId,
        agentId,
        providerId,
        context,
        metadata,
        status: 'ACTIVE'
      },
      include: {
        agent: {
          select: { id: true, name: true, status: true }
        },
        provider: {
          select: { id: true, name: true, type: true }
        }
      }
    })

    // Initialize session in Redis
    await SessionManager.setSession(session.id, {
      userId: user.userId,
      agentId,
      providerId,
      status: 'ACTIVE',
      createdAt: session.createdAt.toISOString()
    })

    if (context) {
      await SessionManager.setContext(session.id, context)
    }

    logger.info(`Session created: ${session.id} by user ${user.userId}`)

    res.status(201).json({
      success: true,
      data: session
    })
  } catch (error) {
    next(error)
  }
})

// Update session
router.put('/:id', requirePermission('sessions:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const updates = UpdateSessionSchema.parse(req.body)

    const existingSession = await prisma.session.findUnique({
      where: { id }
    })

    if (!existingSession) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (existingSession.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const session = await prisma.session.update({
      where: { id },
      data: {
        ...updates,
        updatedAt: new Date()
      },
      include: {
        agent: {
          select: { id: true, name: true, status: true }
        },
        provider: {
          select: { id: true, name: true, type: true }
        }
      }
    })

    // Update Redis context if provided
    if (updates.context) {
      await SessionManager.updateContext(id, updates.context)
    }

    res.json({
      success: true,
      data: session
    })
  } catch (error) {
    next(error)
  }
})

// Delete session
router.delete('/:id', requirePermission('sessions:delete'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const existingSession = await prisma.session.findUnique({
      where: { id }
    })

    if (!existingSession) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (existingSession.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Delete from database (cascade will handle events)
    await prisma.session.delete({
      where: { id }
    })

    // Clean up Redis data
    await SessionManager.deleteSession(id)
    await SessionManager.removeUserSession(user.userId, id)

    logger.info(`Session deleted: ${id} by user ${user.userId}`)

    res.json({
      success: true,
      message: 'Session deleted successfully'
    })
  } catch (error) {
    next(error)
  }
})

// Start agent execution in session
router.post('/:id/start-agent', requirePermission('sessions:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const { agentId, variables } = StartAgentSchema.parse(req.body)

    const session = await prisma.session.findUnique({
      where: { id }
    })

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (session.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    if (session.status !== 'ACTIVE') {
      return res.status(400).json({ error: 'Session is not active' })
    }

    // Validate agent access
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        OR: [
          { userId: user.userId },
          { tenantId: user.tenantId || null }
        ]
      }
    })

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found or access denied' })
    }

    // Update session with agent
    await prisma.session.update({
      where: { id },
      data: { agentId }
    })

    // Start agent execution
    const executionEngine = new AgentExecutionEngine()
    await executionEngine.startExecution(id, user.userId, agentId, variables || {})

    logger.info(`Agent execution started: ${agentId} in session ${id}`)

    res.json({
      success: true,
      message: 'Agent execution started',
      data: {
        sessionId: id,
        agentId,
        variables
      }
    })
  } catch (error) {
    next(error)
  }
})

// Stop agent execution in session
router.post('/:id/stop-agent', requirePermission('sessions:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const session = await prisma.session.findUnique({
      where: { id }
    })

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (session.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Stop agent execution
    const executionEngine = new AgentExecutionEngine()
    await executionEngine.stopExecution(id, 'user_stop')

    logger.info(`Agent execution stopped in session ${id}`)

    res.json({
      success: true,
      message: 'Agent execution stopped'
    })
  } catch (error) {
    next(error)
  }
})

// Get session events
router.get('/:id/events', requirePermission('sessions:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const { type, limit = '100', offset = '0' } = req.query

    const session = await prisma.session.findUnique({
      where: { id },
      select: { userId: true }
    })

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (session.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const where: any = { sessionId: id }
    if (type) where.type = type

    const events = await prisma.event.findMany({
      where,
      orderBy: { timestamp: 'asc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string)
    })

    const total = await prisma.event.count({ where })

    res.json({
      success: true,
      data: {
        events,
        pagination: {
          total,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: total > parseInt(offset as string) + events.length
        }
      }
    })
  } catch (error) {
    next(error)
  }
})

// Get session context from Redis
router.get('/:id/context', requirePermission('sessions:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const session = await prisma.session.findUnique({
      where: { id },
      select: { userId: true }
    })

    if (!session) {
      return res.status(404).json({ error: 'Session not found' })
    }

    if (session.userId !== user.userId) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const context = await SessionManager.getContext(id)
    const memories = await SessionManager.getMemories(id)

    res.json({
      success: true,
      data: {
        context,
        memories
      }
    })
  } catch (error) {
    next(error)
  }
})

export default router
