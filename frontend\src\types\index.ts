// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string | null
  role: 'ADMIN' | 'USER' | 'DEVELOPER'
  tenantId: string | null
  createdAt: string
  updatedAt: string
}

export interface AuthResponse {
  user: User
  token: string
  refreshToken: string
}

// Agent Types
export interface Agent {
  id: string
  name: string
  description: string | null
  config: AgentFlow
  status: 'ACTIVE' | 'INACTIVE' | 'TRAINING' | 'ERROR'
  userId: string
  tenantId: string | null
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    name: string | null
    email: string
  }
  tools?: AgentTool[]
  _count?: {
    sessions: number
  }
}

export interface AgentFlow {
  id: string
  name: string
  description?: string
  nodes: FlowNode[]
  variables?: Record<string, any>
  settings?: {
    maxExecutionTime?: number
    maxIterations?: number
    enableLogging?: boolean
    retryAttempts?: number
  }
}

export interface FlowNode {
  id: string
  type: 'start' | 'condition' | 'action' | 'tool' | 'provider' | 'end'
  position: { x: number; y: number }
  data: Record<string, any>
  connections: Array<{
    targetId: string
    condition?: string
  }>
}

export interface AgentTool {
  id: string
  agentId: string
  toolId: string
  config: Record<string, any> | null
  priority: number
  tool: ToolType
}

// Tool Types
export interface Tool {
  id: string
  name: string
  description: string | null
  type: string
  schema: ToolSchema
  config: Record<string, any>
  status: 'ACTIVE' | 'INACTIVE'
  createdAt: string
  updatedAt: string
  agents?: AgentTool[]
}

export interface ToolSchema {
  type: 'object' | 'string' | 'number' | 'boolean' | 'array'
  properties?: Record<string, any>
  required?: string[]
  items?: any
  description?: string
}

export interface ToolExecutionResult {
  success: boolean
  data?: any
  error?: string
  executionTime: number
  metadata?: Record<string, any>
}

  export interface ToolType{
    id: string
    name: string
    description: string | null
    type: 'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK'
    status: 'ACTIVE' | 'INACTIVE'
  }

// Provider Types
export type ProviderType = 'OPENAI' | 'ANTHROPIC' | 'GOOGLE' | 'CUSTOM'

export interface Provider {
  id: string
  name: string
  type: ProviderType
  config: Record<string, any>
  isActive: boolean
  tenantId: string | null
  createdAt: string
  updatedAt: string
  serviceInfo?: {
    name: string
    models: string[]
    isHealthy: boolean
  }
}

export interface ProviderResponse {
  content: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  finishReason?: string
  responseTime: number
  providerId: string
  model: string
}

// Session Types
export interface Session {
  id: string
  userId: string
  agentId: string | null
  providerId: string | null
  context: Record<string, any> | null
  metadata: Record<string, any> | null
  status: 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'TERMINATED'
  expiresAt: string | null
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    status: string
    config?: AgentFlow
  }
  provider?: {
    id: string
    name: string
    type: string
  }
  events?: Event[]
  _count?: {
    events: number
  }
  redisContext?: Record<string, any>
  memories?: any[]
}

// Event Types
export interface Event {
  id: string
  sessionId: string
  type: EventType
  data: Record<string, any>
  timestamp: string
}

export type EventType = 
  | 'AGENT_START'
  | 'AGENT_STOP'
  | 'TOOL_CALL'
  | 'TOOL_RESPONSE'
  | 'PROVIDER_REQUEST'
  | 'PROVIDER_RESPONSE'
  | 'USER_MESSAGE'
  | 'AGENT_MESSAGE'
  | 'ERROR'
  | 'SYSTEM'

// UAUI Protocol Types
export interface UAUIEvent {
  id: string
  type: string
  timestamp: string
  sessionId: string
  userId?: string
  agentId?: string
  data: Record<string, any>
}

// WebSocket Types
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}

export interface ConnectionStatus {
  connected: boolean
  sessionId?: string
  lastActivity?: string
  reconnectAttempts: number
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> {
  pagination: any
  total: any
  sessions: any
  items: any
  success: boolean
  data: {
    items?: T[]
    sessions?: T[]
    agents?: T[]
    tools?: T[]
    providers?: T[]
    events?: T[]
    pagination: {
      total: number
      limit: number
      offset: number
      hasMore: boolean
    }
  }
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  password: string
  name?: string
  tenantId?: string
}

export interface AgentForm {
  name: string
  description?: string
  config: AgentFlow
  tenantId?: string
}

export interface ToolForm {
  name: string
  description?: string
  type: 'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK'
  schema: ToolSchema
  config: Record<string, any>
}

export interface ProviderForm {
  name: string
  type: ProviderType
  config: Record<string, any>
  tenantId?: string
}

// Chart and Analytics Types
export interface ChartData {
  name: string
  value: number
  date?: string
}

export interface UsageStats {
  totalRequests: number
  uniqueSessions: number
  totalTokens?: number
  recentRequests: number
  dailyUsage: Record<string, number>
  lastUsed: string | null
}

// Theme and UI Types
export interface Theme {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  desktop: boolean
  sound: boolean
}

// Error Types
export interface AppError {
  message: string
  code?: string
  details?: any
  timestamp: string
}

// Loading States
export interface LoadingState {
  isLoading: boolean
  error: string | null
}

// Filter and Search Types
export interface FilterOptions {
  status?: string
  type?: string
  active?: boolean
  dateRange?: {
    start: string
    end: string
  }
  search?: string
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface SearchOptions {
  query: string 
  page: number  
  limit: number
  sort?: SortOptions
  filter?: FilterOptions
}

