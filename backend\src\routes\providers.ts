import { Router } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database'
import { AuthenticatedRequest, requirePermission } from '@/middleware/auth'
import { RBACService } from '@/services/auth'
import { ProviderService } from '@/services/providers'
import { logger } from '@/utils/logger'

const router = Router()

// Validation schemas
const CreateProviderSchema = z.object({
  name: z.string().min(1).max(100),
  type: z.enum(['OPENAI', 'ANTHROPIC', 'GOOGLE', 'CUSTOM']),
  config: z.record(z.any()),
  tenantId: z.string().optional()
})

const UpdateProviderSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  config: z.record(z.any()).optional(),
  isActive: z.boolean().optional()
})

const TestProviderSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant']),
    content: z.string()
  })),
  config: z.object({
    model: z.string().optional(),
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().min(1).max(8192).optional()
  }).optional()
})

const SelectProviderSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant']),
    content: z.string()
  })),
  requirements: z.object({
    maxCost: z.number().optional(),
    preferredModels: z.array(z.string()).optional(),
    excludeProviders: z.array(z.string()).optional(),
    requireVision: z.boolean().optional()
  }).optional()
})

// Get all providers
router.get('/', requirePermission('providers:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { type, active } = req.query

    const where: any = {}
    if (type) where.type = type
    if (active !== undefined) where.isActive = active === 'true'
    if (user.tenantId) where.tenantId = user.tenantId

    const providers = await prisma.provider.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    })

    const filteredProviders = RBACService.filterByTenant(providers, user.tenantId ?? null)

    res.json({
      success: true,
      data: filteredProviders
    })
  } catch (error) {
    next(error)
  }
})

// Get provider by ID
router.get('/:id', requirePermission('providers:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const provider = await prisma.provider.findUnique({
      where: { id: id! }
    })

    if (!provider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, provider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Get provider info from service
    const providerService = new ProviderService()
    const providerInfo = providerService.getProviderInfo(id!)

    res.json({
      success: true,
      data: {
        ...provider,
        serviceInfo: providerInfo
      }
    })
  } catch (error) {
    next(error)
  }
})

// Create provider
router.post('/', requirePermission('providers:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { name, type, config, tenantId } = CreateProviderSchema.parse(req.body)

    const provider = await prisma.provider.create({
      data: {
        name,
        type,
        config,
        tenantId: tenantId || user.tenantId
      }
    })

    logger.info(`Provider created: ${provider.name} by user ${user.userId}`)

    // Refresh provider service to include new provider
    const providerService = new ProviderService()
    await providerService.refreshProviders()

    res.status(201).json({
      success: true,
      data: provider
    })
  } catch (error) {
    next(error)
  }
})

// Update provider
router.put('/:id', requirePermission('providers:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const updates = UpdateProviderSchema.parse(req.body)

    const existingProvider = await prisma.provider.findUnique({
      where: { id: id! }
    })

    if (!existingProvider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, existingProvider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const provider = await prisma.provider.update({
      where: { id: id! },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    })

    logger.info(`Provider updated: ${provider.name} by user ${user.userId}`)

    // Refresh provider service
    const providerService = new ProviderService()
    await providerService.refreshProviders()

    res.json({
      success: true,
      data: provider
    })
  } catch (error) {
    next(error)
  }
})

// Delete provider
router.delete('/:id', requirePermission('providers:delete'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const existingProvider = await prisma.provider.findUnique({
      where: { id: id! }
    })

    if (!existingProvider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId ?? null, existingProvider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Check if provider is being used
    const sessions = await prisma.session.findMany({
      where: { providerId: id, status: 'ACTIVE' }
    })

    if (sessions.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete provider with active sessions',
        activeSessions: sessions.length
      })
    }

    await prisma.provider.delete({
      where: { id }
    })

    logger.info(`Provider deleted: ${existingProvider.name} by user ${user.userId}`)

    // Refresh provider service
    const providerService = new ProviderService()
    await providerService.refreshProviders()

    res.json({
      success: true,
      message: 'Provider deleted successfully'
    })
  } catch (error) {
    next(error)
  }
})

// Test provider
router.post('/:id/test', requirePermission('providers:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const { messages, config } = TestProviderSchema.parse(req.body)

    const provider = await prisma.provider.findUnique({
      where: { id }
    })

    if (!provider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId, provider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    if (!provider.isActive) {
      return res.status(400).json({ error: 'Provider is not active' })
    }

    const providerService = new ProviderService()
    const result = await providerService.generateResponse(id, messages, config)

    logger.info(`Provider tested: ${provider.name} by user ${user.userId}`)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    next(error)
  }
})

// Get provider health status
router.get('/:id/health', requirePermission('providers:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const provider = await prisma.provider.findUnique({
      where: { id }
    })

    if (!provider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId, provider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const providerService = new ProviderService()
    const healthyProviders = providerService.getHealthyProviders()
    const isHealthy = healthyProviders.includes(id)

    res.json({
      success: true,
      data: {
        providerId: id,
        isHealthy,
        lastChecked: new Date().toISOString()
      }
    })
  } catch (error) {
    next(error)
  }
})

// Select best provider for request
router.post('/select', requirePermission('providers:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { messages, requirements } = SelectProviderSchema.parse(req.body)

    const providerService = new ProviderService()

    // Filter providers by tenant access
    const userProviders = await prisma.provider.findMany({
      where: {
        isActive: true,
        ...(user.tenantId ? { tenantId: user.tenantId } : {})
      },
      select: { id: true }
    })

    const availableProviderIds = userProviders.map(p => p.id)

    // Exclude providers user doesn't have access to
    const modifiedRequirements = {
      ...requirements,
      excludeProviders: [
        ...(requirements?.excludeProviders || []),
        ...providerService.getHealthyProviders().filter(id => !availableProviderIds.includes(id))
      ]
    }

    const selectedProviderId = await providerService.selectBestProvider(messages, modifiedRequirements)

    const selectedProvider = await prisma.provider.findUnique({
      where: { id: selectedProviderId },
      select: {
        id: true,
        name: true,
        type: true
      }
    })

    res.json({
      success: true,
      data: {
        providerId: selectedProviderId,
        provider: selectedProvider,
        requirements: modifiedRequirements
      }
    })
  } catch (error) {
    next(error)
  }
})

// Get provider usage statistics
router.get('/:id/stats', requirePermission('providers:read'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    const provider = await prisma.provider.findUnique({
      where: { id }
    })

    if (!provider) {
      return res.status(404).json({ error: 'Provider not found' })
    }

    if (!RBACService.canAccessTenant(user.tenantId, provider.tenantId)) {
      return res.status(403).json({ error: 'Access denied' })
    }

    // Get usage statistics from events
    const providerEvents = await prisma.event.findMany({
      where: {
        type: 'PROVIDER_REQUEST',
        data: {
          path: ['providerId'],
          equals: id
        }
      },
      select: {
        timestamp: true,
        sessionId: true,
        data: true
      },
      orderBy: { timestamp: 'desc' },
      take: 1000
    })

    const uniqueSessions = new Set(providerEvents.map(e => e.sessionId)).size
    const totalRequests = providerEvents.length

    // Calculate token usage if available
    let totalTokens = 0
    providerEvents.forEach(event => {
      const usage = (event.data as any)?.usage
      if (usage?.totalTokens) {
        totalTokens += usage.totalTokens
      }
    })

    // Calculate usage over time (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentEvents = providerEvents.filter(e => e.timestamp >= thirtyDaysAgo)
    const dailyUsage = recentEvents.reduce((acc, event) => {
      const date = event.timestamp.toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    res.json({
      success: true,
      data: {
        providerId: id,
        totalRequests,
        uniqueSessions,
        totalTokens,
        recentRequests: recentEvents.length,
        dailyUsage,
        lastUsed: providerEvents[0]?.timestamp || null
      }
    })
  } catch (error) {
    next(error)
  }
})

export default router
