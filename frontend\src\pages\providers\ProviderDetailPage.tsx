import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Edit, Trash2, Activity, Settings, TestTube, BarChart3 } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Provider } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import  Badge  from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import JsonViewer from '@/components/ui/JsonViewer'

const ProviderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [provider, setProvider] = useState<Provider | null>(null)
  const [loading, setLoading] = useState(true)
  const [health, setHealth] = useState<any>(null)
  const [stats, setStats] = useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  useEffect(() => {
    if (id) {
      loadProvider()
      loadHealth()
      loadStats()
    }
  }, [id])

  const loadProvider = async () => {
    try {
      setLoading(true)
      const data = await api.getProvider(id!)
      setProvider(data)
    } catch (error) {
      console.error('Failed to load provider:', error)
      toast.error('Failed to load provider')
      navigate('/providers')
    } finally {
      setLoading(false)
    }
  }

  const loadHealth = async () => {
    try {
      const data = await api.getProviderHealth(id!)
      setHealth(data)
    } catch (error) {
      console.error('Failed to load provider health:', error)
    }
  }

  const loadStats = async () => {
    try {
      const data = await api.getProviderStats(id!)
      setStats(data)
    } catch (error) {
      console.error('Failed to load provider stats:', error)
    }
  }

  const handleDelete = async () => {
    if (!provider) return

    try {
      setDeleting(true)
      await api.deleteProvider(provider.id)
      toast.success('Provider deleted successfully')
      navigate('/providers')
    } catch (error) {
      console.error('Failed to delete provider:', error)
      toast.error('Failed to delete provider')
    } finally {
      setDeleting(false)
    }
  }

  const handleTest = async () => {
    if (!provider) return

    try {
      setTesting(true)
      const testMessages = [
        { role: 'user', content: 'Hello, this is a test message to verify the provider is working correctly.' }
      ]
      const result = await api.testProvider(provider.id, testMessages)
      setTestResult(result)
      toast.success('Provider test completed successfully')
    } catch (error) {
      console.error('Failed to test provider:', error)
      toast.error('Failed to test provider')
    } finally {
      setTesting(false)
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'OPENAI': return 'bg-green-100 text-green-800'
      case 'ANTHROPIC': return 'bg-orange-100 text-orange-800'
      case 'GOOGLE': return 'bg-blue-100 text-blue-800'
      case 'CUSTOM': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getHealthColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'healthy': return 'bg-green-100 text-green-800'
      case 'unhealthy': return 'bg-red-100 text-red-800'
      case 'degraded': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!provider) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">Provider not found</div>
        <Button onClick={() => navigate('/providers')}>
          Back to Providers
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/providers')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{provider.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge className={getTypeColor(provider.type)}>
                {provider.type}
              </Badge>
              <Badge className={provider.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {provider.isActive ? 'Active' : 'Inactive'}
              </Badge>
              {health && (
                <Badge className={getHealthColor(health.status)}>
                  {health.status}
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleTest} disabled={testing}>
            <TestTube className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Test Provider'}
          </Button>
          <Button variant="outline" onClick={() => navigate(`/providers/${provider.id}/edit`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="health">Health</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Provider Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <Badge className={getTypeColor(provider.type)}>{provider.type}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge className={provider.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {provider.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Scope:</span>
                  <span>{provider.tenantId ? 'Tenant Specific' : 'Global'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(provider.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Updated:</span>
                  <span>{new Date(provider.updatedAt).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>

            {testResult && (
              <Card>
                <CardHeader>
                  <CardTitle>Last Test Result</CardTitle>
                </CardHeader>
                <CardContent>
                  <JsonViewer data={testResult} />
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="config" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Provider Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <JsonViewer data={provider.config} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Health Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {health ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Status:</span>
                    <Badge className={getHealthColor(health.status)}>
                      {health.status}
                    </Badge>
                  </div>
                  <JsonViewer data={health} />
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Button onClick={loadHealth} variant="outline">
                    Check Health Status
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Usage Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats ? (
                <JsonViewer data={stats} />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No statistics available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Provider</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{provider.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ProviderDetailPage
